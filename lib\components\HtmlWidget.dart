import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../components/PdfViewWidget.dart';
import '../components/TableViewWidget.dart';
import '../components/VimeoEmbedWidget.dart';
import '../components/YouTubeEmbedWidget.dart';
import '../../../utils/extension.dart';
import 'package:nb_utils/nb_utils.dart';
import 'AppWidgets.dart';
import 'TweetWidget.dart';

class HtmlWidget extends StatefulWidget {
  final String? postContent;
  final Color? color;

  HtmlWidget({this.postContent, this.color});

  @override
  _HtmlWidgetState createState() => _HtmlWidgetState();
}

class _HtmlWidgetState extends State<HtmlWidget> {
  bool _isMounted = false;

  @override
  void initState() {
    super.initState();
    _isMounted = true;
  }

  @override
  void dispose() {
    _isMounted = false;
    super.dispose();
  }

  /// Fixes URLs to ensure they have a valid scheme (http/https)
  String fixUrl(String? url) {
    if (url == null || url.trim().isEmpty) return "";
    url = url.trim();

    if (url.startsWith("//")) return "https:$url";

    return url;
  }

  @override
  Widget build(BuildContext context) {
    return Html(
      data: widget.postContent ?? "",
      onLinkTap: (url, _, __, ___) async {
        if (url == null || url.isEmpty || !_isMounted) return;
        String fixedUrl = fixUrl(url);

        if (!mounted) return;
        if (fixedUrl.endsWith('.pdf')) {
          await PdfViewWidget(pdfUrl: fixedUrl).launch(context);
        } else {
          await launchUrls(fixedUrl, forceWebView: false);
        }
      },
      onImageTap: (url, _, __, ___) {
        if (url == null || url.isEmpty || !_isMounted) return;
        String fixedUrl = fixUrl(url);
        openPhotoViewer(context, Image.network(fixedUrl).image);
      },
      style: {
        "table": Style(backgroundColor: widget.color ?? transparentColor),
        "tr": Style(border: Border(bottom: BorderSide(color: Colors.black45.withOpacity(0.5)))),
        "th": Style(padding: EdgeInsets.all(6), backgroundColor: Colors.black45.withOpacity(0.5)),
        "td": Style(padding: EdgeInsets.all(6), alignment: Alignment.center),
        'a': Style(color: widget.color ?? Colors.blue, fontWeight: FontWeight.bold),
        'img': Style(width: context.width(), padding: EdgeInsets.only(bottom: 8)),
        'li': Style(
          color: widget.color ?? textPrimaryColorGlobal,
          listStyleType: ListStyleType.DISC,
          listStylePosition: ListStylePosition.OUTSIDE,
        ),
      },
      customRender: {
        "img": (RenderContext renderContext, Widget child) {
          String img = renderContext.tree.attributes['src'] ?? "";
          img = fixUrl(img);
          return cachedImage(img).cornerRadiusWithClipRRect(defaultRadius).onTap(() {
            openPhotoViewer(context, NetworkImage(img));
          });
        },
        "iframe": (RenderContext renderContext, Widget child) {
          String? src = renderContext.tree.attributes['src'];
          if (src != null) {
            return YouTubeEmbedWidget(fixUrl(src).toYouTubeId());
          }
          return child;
        },
        "blockquote": (RenderContext renderContext, Widget child) {
          return TweetWebView(tweetUrl: renderContext.tree.element!.outerHtml);
        },
        "embed": (RenderContext renderContext, Widget child) {
          var videoLink = renderContext.parser.htmlData.text.splitBetween('<embed>', '</embed>');
          if (videoLink.contains('youtu.be')) {
            return YouTubeEmbedWidget(fixUrl(videoLink).toYouTubeId());
          } else if (videoLink.contains('vimeo')) {
            return VimeoEmbedWidget(fixUrl(videoLink));
          }
          return child;
        },
        "table": (RenderContext renderContext, Widget child) {
          return Column(
            children: [
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: Icon(Icons.open_in_full_rounded),
                  onPressed: () async {
                    await TableViewWidget(renderContext).launch(context);
                    if (!mounted) return;
                    setOrientationPortrait();
                  },
                ),
              ),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: (renderContext.tree as TableSectionLayoutElement).toWidget(renderContext),
              ),
            ],
          );
        },
      },
    );
  }
}
