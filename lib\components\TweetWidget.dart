import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TweetWebView extends StatefulWidget {
  final String? tweetUrl;
  final double? aspectRatio;

  TweetWebView({this.tweetUrl, this.aspectRatio});

  @override
  _TweetWebViewState createState() => _TweetWebViewState();
}

class _TweetWebViewState extends State<TweetWebView> {
  late final WebViewController wbController;
  String? _postHTML;
  double _height = 450;
  String lastUrl = "https://twitter.com";

  @override
  void initState() {
    super.initState();

    _postHTML = widget.tweetUrl ?? "";

    if (_postHTML!.isNotEmpty) {
      lastUrl = extractLastUrl(_postHTML!);
    }

    print("Last URL is ==> $lastUrl");

    if (lastUrl.isNotEmpty && isValidUrl(lastUrl)) {
      wbController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(const Color(0x00000000))
        ..addJavaScriptChannel(
          'PageHeight',
          onMessageReceived: (JavaScriptMessage message) {
            try {
              _setHeight(double.parse(message.message));
            } catch (e) {
              print("Error parsing height: $e");
            }
          },
        )
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageFinished: (String url) {
              final color = colorToHtmlRGBA(getBackgroundColor(context));
              wbController.runJavaScript('document.body.style= "background-color: $color"');
              wbController.runJavaScript('setTimeout(() => sendHeight(), 0)');
            },
            onNavigationRequest: (NavigationRequest request) async {
              // Allow only Twitter embeds to load in WebView
              if (request.url.contains("twitter.com") && !request.url.contains("/status/")) {
                return NavigationDecision.navigate;
              }
              return NavigationDecision.prevent;
            },
          ),
        );

      wbController.loadRequest(Uri.parse(lastUrl));
    } else {
      print("Invalid URL: $lastUrl");
    }
  }

  /// Extracts the last valid URL from the text
  String extractLastUrl(String input) {
    final urlPattern = RegExp(r'(https?://[^\s]+)', caseSensitive: false);
    final matches = urlPattern.allMatches(input);

    if (matches.isNotEmpty) {
      return matches.last.group(0) ?? "https://twitter.com";
    }
    return "https://twitter.com";
  }

  /// Checks if a URL is valid
  bool isValidUrl(String url) {
    return Uri.tryParse(url)?.hasScheme ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final wv = WebViewWidget(controller: wbController);
    final ar = widget.aspectRatio;

    return (ar != null)
        ? ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height / 1.5,
        maxWidth: context.width(),
      ),
      child: AspectRatio(aspectRatio: ar, child: wv),
    )
        : SizedBox(height: _height, child: wv, width: context.width());
  }

  void _setHeight(double height) {
    setState(() {
      _height = height;
    });
  }

  String colorToHtmlRGBA(Color c) {
    return 'rgba(${c.red},${c.green},${c.blue},${c.alpha / 255})';
  }

  Color getBackgroundColor(BuildContext context) {
    return Theme.of(context).scaffoldBackgroundColor;
  }
}
