name: mighty_news
description: A new Flutter application.

publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  #Basic
  nb_utils: ^7.0.9
  google_fonts: ^6.2.1
  flutter_vector_icons: ^2.0.0

  #UI
  fading_edge_scrollview: ^4.1.1
  shimmer: ^3.0.0
  photo_view: ^0.15.0
  story_view: ^0.16.5
  rive: ^0.13.20
  clippy_flutter: 2.0.0-nullsafety.1
  video_player: ^2.9.2

  #Network
  http: ^1.3.0
  intl: ^0.20.1
  html: ^0.15.5
  flutter_html:
    git:
      url: https://github.com/appmeetmighty/flutter_html.git
      ref: master


  #State
  mobx: ^2.5.0
  flutter_mobx: ^2.3.0
  package_info: ^2.0.2

  #One Signal
  onesignal_flutter: ^5.2.9

  #Firebase
  firebase_core: ^3.10.1
  firebase_auth: ^5.4.1
  firebase_remote_config: ^5.3.1
  firebase_crashlytics: ^4.3.1

  #AdMob
  google_mobile_ads: ^5.3.0
  facebook_audience_network: ^1.0.1

  #Social Login
  google_sign_in: ^6.2.2
  otp_text_field: ^1.1.3
  country_code_picker: ^3.1.0
  the_apple_sign_in: ^1.1.1

  #Other
  cached_network_image: ^3.4.1
  geolocator: ^13.0.2
  flutter_tts: ^4.2.1
  image_picker: ^1.1.2
  share: ^2.0.4
  url_launcher: ^6.3.1

  flutter_svg : ^2.0.10+1
  connectivity_plus: ^6.1.2
  dbus: ^0.7.11
  ffi: ^2.1.3
  xml: ^6.5.0

  flutter_localizations:
    sdk: flutter


dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.14
  mobx_codegen: ^2.7.0
  #flutter packages pub run build_runner build --delete-conflicting-outputs

dependency_overrides:
  intl: ^0.18.1
  xml: ^6.2.2
  html: ^0.15.4
  webview_flutter: ^4.8.0
  nb_utils: ^6.0.6
  flutter_math_fork: ^0.7.2
  flutter_svg: ^1.0.0
  flutter_layout_grid: ^2.0.7
  connectivity_plus: ^5.0.2
  google_mobile_ads: ^2.4.0

flutter:

  uses-material-design: true

  assets:
    - assets/
    - assets/flags/
    - lang/
    - assets/rive/