import 'package:flutter/material.dart';
import '../../../models/DashboardResponse.dart';
import '../../../utils/extension.dart' as m;

import 'package:nb_utils/nb_utils.dart';

import '../main.dart';
import '../utils/extension.dart';

class BreakingNewsMarqueeWidget extends StatefulWidget {
  static String tag = '/BreakingNewsMarqueeWidget';
  final List<NewsData>? data;

  BreakingNewsMarqueeWidget({Key? key, this.data}) : super(key: key);

  @override
  BreakingNewsMarqueeWidgetState createState() => BreakingNewsMarqueeWidgetState();
}

class BreakingNewsMarqueeWidgetState extends State<BreakingNewsMarqueeWidget> {
  String mBreakingNewsMarquee = '';

  void _updateMarqueeText() {
    mBreakingNewsMarquee = '';
    widget.data?.forEach((element) {
      if (widget.data!.indexOf(element) != 0) {
        mBreakingNewsMarquee += '   |   ';
      } else {
        mBreakingNewsMarquee += '     ';
      }

      mBreakingNewsMarquee += element.post_title!;
    });

    if (getStringAsync(COPYRIGHT_TEXT).isNotEmpty) {
      mBreakingNewsMarquee += '  |  ' + getStringAsync(COPYRIGHT_TEXT);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateMarqueeText();
  }

  @override
  Widget build(BuildContext context) {
    if (getBoolAsync(DISABLE_HEADLINE_WIDGET)) return SizedBox();
    if (widget.data?.isEmpty ?? true) return SizedBox();
    _updateMarqueeText();

    return Container(
      height: 45.0,
      width: context.width(),
      decoration: BoxDecoration(
        boxShadow: defaultBoxShadow(),
        color: appStore.isDarkMode ? black : white,
      ),
      child: m.Marquee(
        text: mBreakingNewsMarquee,
        style: boldTextStyle(),
        pauseAfterRound: 2.seconds,
        startAfter: 1.seconds,
      ),
    );
  }
}
