PODS:
  - AppAuth (1.5.0):
    - AppAuth/Core (= 1.5.0)
    - AppAuth/ExternalUserAgent (= 1.5.0)
  - AppAuth/Core (1.5.0)
  - AppAuth/ExternalUserAgent (1.5.0):
    - AppAuth/Core
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - facebook_audience_network (0.0.1):
    - FBAudienceNetwork (~> 6.3)
    - Flutter
  - FBAudienceNetwork (6.11.1)
  - Firebase/Auth (8.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 8.15.0)
  - Firebase/CoreOnly (8.15.0):
    - FirebaseCore (= 8.15.0)
  - Firebase/Crashlytics (8.15.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 8.15.0)
  - Firebase/RemoteConfig (8.15.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 8.15.0)
  - firebase_auth (3.3.19):
    - Firebase/Auth (= 8.15.0)
    - firebase_core
    - Flutter
  - firebase_core (1.17.1):
    - Firebase/CoreOnly (= 8.15.0)
    - Flutter
  - firebase_crashlytics (2.8.1):
    - Firebase/Crashlytics (= 8.15.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (2.0.8):
    - Firebase/RemoteConfig (= 8.15.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (8.15.0):
    - FirebaseCore (~> 8.0)
  - FirebaseAuth (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/Environment (~> 7.7)
    - GTMSessionFetcher/Core (~> 1.5)
  - FirebaseCore (8.15.0):
    - FirebaseCoreDiagnostics (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
  - FirebaseCoreDiagnostics (8.15.0):
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/Logger (~> 7.7)
    - nanopb (~> 2.30908.0)
  - FirebaseCrashlytics (8.15.0):
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleDataTransport (~> 9.1)
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (~> 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseInstallations (8.15.0):
    - FirebaseCore (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - GoogleUtilities/UserDefaults (~> 7.7)
    - PromisesObjC (< 3.0, >= 1.2)
  - FirebaseRemoteConfig (8.15.0):
    - FirebaseABTesting (~> 8.0)
    - FirebaseCore (~> 8.0)
    - FirebaseInstallations (~> 8.0)
    - GoogleUtilities/Environment (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
  - Flutter (1.0.0)
  - flutter_tts (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - geolocator_apple (1.2.0):
    - Flutter
  - Google-Mobile-Ads-SDK (8.13.0):
    - GoogleAppMeasurement (< 9.0, >= 7.0)
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (0.0.1):
    - Flutter
    - Google-Mobile-Ads-SDK (= 8.13.0)
  - google_sign_in_ios (0.0.1):
    - Flutter
    - GoogleSignIn (~> 6.2)
  - GoogleAppMeasurement (8.15.0):
    - GoogleAppMeasurement/AdIdSupport (= 8.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (8.15.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 8.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (8.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (~> 2.30908.0)
  - GoogleDataTransport (9.1.4):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (6.2.1):
    - AppAuth (~> 1.5)
    - GTMAppAuth (~> 1.3)
    - GTMSessionFetcher/Core (~> 1.1)
  - GoogleUserMessagingPlatform (2.0.0)
  - GoogleUtilities/AppDelegateSwizzler (7.7.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.7.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.7.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.7.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.7.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.7.0)"
  - GoogleUtilities/Reachability (7.7.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.7.0):
    - GoogleUtilities/Logger
  - GTMAppAuth (1.3.0):
    - AppAuth/Core (~> 1.4)
    - GTMSessionFetcher/Core (~> 1.5)
  - GTMSessionFetcher/Core (1.7.2)
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (2.30908.0):
    - nanopb/decode (= 2.30908.0)
    - nanopb/encode (= 2.30908.0)
  - nanopb/decode (2.30908.0)
  - nanopb/encode (2.30908.0)
  - nb_utils (0.0.1):
    - Flutter
  - onesignal_flutter (3.3.1):
    - Flutter
    - OneSignalXCFramework (= 3.11.1)
  - OneSignalXCFramework (3.11.1):
    - OneSignalXCFramework/OneSignalCore (= 3.11.1)
    - OneSignalXCFramework/OneSignalExtension (= 3.11.1)
    - OneSignalXCFramework/OneSignalOutcomes (= 3.11.1)
  - OneSignalXCFramework/OneSignalCore (3.11.1)
  - OneSignalXCFramework/OneSignalExtension (3.11.1):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOutcomes (3.11.1):
    - OneSignalXCFramework/OneSignalCore
  - package_info (0.0.1):
    - Flutter
  - path_provider_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.1.0)
  - ReachabilitySwift (5.0.0)
  - share (0.0.1):
    - Flutter
  - shared_preferences_ios (0.0.1):
    - Flutter
  - sqflite (0.0.2):
    - Flutter
    - FMDB (>= 2.7.5)
  - the_apple_sign_in (1.0.0):
    - Flutter
  - Toast (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - facebook_audience_network (from `.symlinks/plugins/facebook_audience_network/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - nb_utils (from `.symlinks/plugins/nb_utils/ios`)
  - onesignal_flutter (from `.symlinks/plugins/onesignal_flutter/ios`)
  - OneSignalXCFramework (< 4.0, >= 3.4.3)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - path_provider_ios (from `.symlinks/plugins/path_provider_ios/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - shared_preferences_ios (from `.symlinks/plugins/shared_preferences_ios/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - the_apple_sign_in (from `.symlinks/plugins/the_apple_sign_in/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - FBAudienceNetwork
    - Firebase
    - FirebaseABTesting
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreDiagnostics
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfig
    - FMDB
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - nanopb
    - OneSignalXCFramework
    - PromisesObjC
    - ReachabilitySwift
    - Toast

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  facebook_audience_network:
    :path: ".symlinks/plugins/facebook_audience_network/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  nb_utils:
    :path: ".symlinks/plugins/nb_utils/ios"
  onesignal_flutter:
    :path: ".symlinks/plugins/onesignal_flutter/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  path_provider_ios:
    :path: ".symlinks/plugins/path_provider_ios/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  shared_preferences_ios:
    :path: ".symlinks/plugins/shared_preferences_ios/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  the_apple_sign_in:
    :path: ".symlinks/plugins/the_apple_sign_in/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  AppAuth: 80317d99ac7ff2801a2f18ff86b48cd315ed465d
  connectivity_plus: 413a8857dd5d9f1c399a39130850d02fe0feaf7e
  facebook_audience_network: bb22bdda913d419d8c3247c9570bda4563302417
  FBAudienceNetwork: 78c7565dbc7df3a24013b4dccdc69a0d0ded8404
  Firebase: 5f8193dff4b5b7c5d5ef72ae54bb76c08e2b841d
  firebase_auth: a6470a6974d42f83117932c913aecb926182f907
  firebase_core: 318de541b0e61d3f24262982a3f0b54afe72439b
  firebase_crashlytics: 3ccd7b4f26a7fc546774b1074d2d10b0c6f92ab6
  firebase_remote_config: 896fe29e06b21d0f4f4b19cc435584ba7386293f
  FirebaseABTesting: 10cbce8db9985ae2e3847ea44e9947dd18f94e10
  FirebaseAuth: 3e73bf8abf4fbb40f8b421f361f4cc48ee57388c
  FirebaseCore: 5743c5785c074a794d35f2fff7ecc254a91e08b1
  FirebaseCoreDiagnostics: 92e07a649aeb66352b319d43bdd2ee3942af84cb
  FirebaseCrashlytics: feb07e4e9187be3c23c6a846cce4824e5ce2dd0b
  FirebaseInstallations: 40bd9054049b2eae9a2c38ef1c3dd213df3605cd
  FirebaseRemoteConfig: 2d6e2cfdb49af79535c8af8a80a4a5009038ec2b
  Flutter: 50d75fe2f02b26cc09d224853bb45737f8b3214a
  flutter_tts: 0f492aab6accf87059b72354fcb4ba934304771d
  fluttertoast: 16fbe6039d06a763f3533670197d01fc73459037
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  geolocator_apple: cc556e6844d508c95df1e87e3ea6fa4e58c50401
  Google-Mobile-Ads-SDK: 05e5d68bb42a61b2e5bef336a52789785605aa22
  google_mobile_ads: 36eaaacc5c08ca2b9c4878232ab98152b11805cc
  google_sign_in_ios: 90eec6616c4c2105b9f9fe5c774773d13565b504
  GoogleAppMeasurement: 4c19f031220c72464d460c9daa1fb5d1acce958e
  GoogleDataTransport: 5fffe35792f8b96ec8d6775f5eccd83c998d5a3b
  GoogleSignIn: 9c69f4188921d8f789816e4c99ba2a1f5f868ddc
  GoogleUserMessagingPlatform: ab890ce5f6620f293a21b6bdd82e416a2c73aeca
  GoogleUtilities: e0913149f6b0625b553d70dae12b49fc62914fd1
  GTMAppAuth: 4d8f864896f3646f0c33baf38a28362f4c601e15
  GTMSessionFetcher: 5595ec75acf5be50814f81e9189490412bad82ba
  image_picker_ios: b786a5dcf033a8336a657191401bfdf12017dabb
  nanopb: a0ba3315591a9ae0a16a309ee504766e90db0c96
  nb_utils: ada4338858d8827ec92fdab2a545206b4ba4cfb1
  onesignal_flutter: 8f17fbc764b033117d2c878db635c3cd4911ff04
  OneSignalXCFramework: 8bdcaa3b7f6082565bf29c5481435376a33e94f2
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  path_provider_ios: 14f3d2fd28c4fdb42f44e0f751d12861c43cee02
  PromisesObjC: 99b6f43f9e1044bd87a95a60beff28c2c44ddb72
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  share: 0b2c3e82132f5888bccca3351c504d0003b3b410
  shared_preferences_ios: 548a61f8053b9b8a49ac19c1ffbc8b92c50d68ad
  sqflite: 6d358c025f5b867b29ed92fc697fd34924e11904
  the_apple_sign_in: 2e78c83cdb09eba07bb16dcc1f3bc12fcdc8263d
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  url_launcher_ios: 839c58cdb4279282219f5e248c3321761ff3c4de
  video_player_avfoundation: e489aac24ef5cf7af82702979ed16f2a5ef84cff
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: 005fbd90c888a42c5690919a1527ecc6649e1162

PODFILE CHECKSUM: 1271eca2d8b3767c6abf6962ab6639c2f238ad66

COCOAPODS: 1.11.3
