{"password_length": "Password length should be more than six", "password_not_match": "Password does not match", "language": "Language", "quick_read": "Quick Read", "quick_read_desc": "In Hurry? Read article like a pro.", "stories": "Stories", "choose_app_language": "Choose your app language", "enable_push_notification": "Enable/ disable push notification", "choose_article_size": "Choose your article font size", "select_tts_language": "Select TTS language same as news language", "tts_language_confirm": "is now default language for Text To Speech", "choose_app_theme": "Choose your app theme", "text_to_speech": "Text To Speech", "category": "Category", "about": "About", "choose_dashboard_page_variant": "Choose Dashboard Page V<PERSON>t", "select_theme": "Select Theme", "remember_me": "Remember Me", "send_otp": "Send OTP", "email_is_invalid": "<PERSON><PERSON> is invalid", "comments": "comments", "comment": "comment", "variant": "<PERSON><PERSON><PERSON>", "choose_detail_page_variant": "<PERSON><PERSON> Detail <PERSON> V<PERSON>t", "our_twitter_handle": "Our Twitter Handle", "confirm": "Confirm", "enter_otp_received": "Enter OTP you received on your number", "include_country_code": "Include country code", "phone_number": "Phone Number", "enter_your_phone": "Enter your phone number", "both_field_required": "Both fields are required", "sign_up_to_continue": "Sign up to continue using", "article_font_size": "Article Font Size", "related_news": "Related News", "app_name": "Mighty News", "walk_Title1": "Subscribe for updates", "walk_Title2": "Choose your category", "walk_Title3": "Daily Notifications", "walk_SubTitle1": "Mighty News offers an awesome category filter option which will let you access what you are interested in straight away!", "walk_SubTitle2": "Mighty News is a complete set of incredible, easily importable UI and get regular updates by subscribing to it!", "walk_SubTitle3": "Notify your users with the latest news with a daily push notification feature. They are highly interactive and useful.", "skip": "SKIP", "next": "NEXT", "finish": "FINISH", "login_Title": "Login to Continue", "login": "<PERSON><PERSON>", "sign_Up": "Sign Up", "forgot_pwd": "Forgot password?", "email": "Email", "password": "Password", "signUp_Title": "Sign Up to Continue", "first_Name": "First Name", "last_Name": "Last Name", "username": "Username", "confirm_Pwd": "Confirm password", "signUp": "Sign Up", "your_Briefing": "Your briefing", "breaking_News": "BREAKING NEWS", "recent_News": "RECENT NEWS", "choose_Topics": "Choose Topics", "suggest_for_you": "Suggested For You", "search_hintText": "Search News", "change_Pwd": "Change Password", "my_Topics": "My Topics", "term_condition": "Terms & condition", "help_Support": "Help & Support", "logout": "Logout", "version": "Version", "email_Validation": "<PERSON><PERSON> is invalid", "password_Validation": "Minimum password length should be", "field_Required": "This field is required", "latest_news": "Latest News", "bookmarks": "Bookmarks", "logout_confirmation": "Do you want to logout from the app?", "NEWS": "NEWS", "view_Comments": "View Comments", "confirm_Password": "Confirm Password", "submit": "Submit", "new_password": "New Password", "edit_Profile": "Edit Profile", "change_avatar": "Change Avatar", "save": "Save", "privacyPolicy": "Privacy Policy", "confirmPwdValidation": "Both password should be matched", "Bookmarks": "Bookmarks", "Comments": "Comments", "no_data": "No Data Found", "post_comment": "Post Comment", "write_here": "Write here", "write_your_comment": "Please write your comment", "post": "Post", "write_comment": "Please write your comment", "view_all": "View All", "enable": "Enable", "disable": "Disable", "push_notification": "Push Notification", "exit_app": "Press back again to exit app", "notification": "Notification", "dark_mode": "Dark Mode", "yes": "Yes", "no": "No", "app_settings": "APP SETTINGS", "other": "Other", "videos": "Videos", "share": "Share", "rate_us": "Rate Us", "delete_dialog": "Are you sure you want to delete", "please_log_in": "Please Log In", "swipe_right_to_delete": "Swipe right to delete ", "delete_account": "Delete Account", "are_you_sure_you_want_to_delete_this_account": "Are you sure you want to delete this account?"}